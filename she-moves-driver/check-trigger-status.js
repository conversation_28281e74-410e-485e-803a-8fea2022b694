#!/usr/bin/env node

/**
 * Check if the database trigger was applied correctly
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

async function checkTriggerStatus() {
  console.log('🔍 Checking database trigger status...\n');

  try {
    // 1. Check if the function exists
    console.log('1. Checking if handle_new_user function exists...');
    
    // Try a simple query to see if we can access system tables
    try {
      const { data: testQuery, error: testError } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);
        
      if (testError) {
        console.error('❌ Cannot access database:', testError);
        return;
      }
      console.log('✅ Database connection working');
    } catch (error) {
      console.error('❌ Database connection failed:', error);
      return;
    }

    // 2. Check recent user creation
    console.log('\n2. Checking recent user creation...');
    
    const newUserId = '9da5a98d-e924-49b1-b406-65b0831445f8';
    console.log(`   New user ID: ${newUserId}`);

    // Check if profile exists
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', newUserId)
      .single();

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('❌ Error checking profile:', profileError);
      return;
    }

    if (profile) {
      console.log('✅ Profile exists:');
      console.log(`   Email: ${profile.email}`);
      console.log(`   Name: ${profile.full_name}`);
      console.log(`   Type: ${profile.user_type}`);
      console.log(`   Created: ${profile.created_at}`);
    } else {
      console.log('❌ Profile does not exist');
      return;
    }

    // Check if driver record exists
    const { data: driver, error: driverError } = await supabase
      .from('drivers')
      .select('*')
      .eq('user_id', newUserId)
      .single();

    if (driverError && driverError.code !== 'PGRST116') {
      console.error('❌ Error checking driver:', driverError);
      return;
    }

    if (driver) {
      console.log('✅ Driver record exists:');
      console.log(`   Driver ID: ${driver.id}`);
      console.log(`   License: ${driver.license_number}`);
      console.log(`   Vehicle: ${driver.vehicle_make} ${driver.vehicle_model}`);
      console.log(`   Status: ${driver.verification_status}`);
      console.log(`   Created: ${driver.created_at}`);
    } else {
      console.log('❌ Driver record does NOT exist');
      console.log('   → This means the trigger did not create the driver profile');
    }

    // 3. Test trigger manually
    console.log('\n3. Testing trigger behavior...');
    
    if (profile && !driver) {
      console.log('❌ TRIGGER ISSUE CONFIRMED:');
      console.log('   - Profile exists (created manually by app)');
      console.log('   - Driver record missing (trigger failed)');
      console.log('');
      console.log('💡 POSSIBLE CAUSES:');
      console.log('   1. Trigger function has syntax errors');
      console.log('   2. Trigger was not created properly');
      console.log('   3. RLS policies blocking trigger execution');
      console.log('   4. Database permissions issue');
    }

    // 4. Check if we can create driver record manually
    console.log('\n4. Testing manual driver record creation...');
    
    if (profile && !driver) {
      const testDriverData = {
        user_id: newUserId,
        license_number: `PENDING-${newUserId.substring(0, 8)}`,
        vehicle_make: 'Not Specified',
        vehicle_model: 'Not Specified',
        vehicle_year: 2020,
        vehicle_color: 'Not Specified',
        vehicle_plate: `PENDING-${newUserId.substring(0, 6)}`,
        vehicle_type: 'SheRide',
        verification_status: 'pending',
        onboarding_completed: false
      };

      const { data: testDriver, error: testDriverError } = await supabase
        .from('drivers')
        .insert(testDriverData)
        .select()
        .single();

      if (testDriverError) {
        console.log('❌ Manual driver creation failed:');
        console.log(`   Error: ${testDriverError.message}`);
        console.log(`   Code: ${testDriverError.code}`);
      } else {
        console.log('✅ Manual driver creation successful:');
        console.log(`   Driver ID: ${testDriver.id}`);
        console.log('   → This confirms the trigger should work but isn\'t running');
      }
    }

    console.log('\n📋 SUMMARY:');
    console.log('');
    if (profile && driver) {
      console.log('✅ Everything working correctly');
    } else if (profile && !driver) {
      console.log('❌ TRIGGER NOT WORKING');
      console.log('   → Need to fix the database trigger');
    } else {
      console.log('❌ PROFILE CREATION FAILED');
      console.log('   → Need to fix profile creation first');
    }

  } catch (error) {
    console.error('❌ Unexpected error:', error);
  }
}

// Run the check
checkTriggerStatus().then(() => {
  console.log('\n🏁 Check complete');
  process.exit(0);
}).catch(error => {
  console.error('💥 Check failed:', error);
  process.exit(1);
});
